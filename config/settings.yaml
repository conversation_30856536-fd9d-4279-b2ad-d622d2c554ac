# 网络数据包分析工具配置文件
# Network Packet Analysis Tool Configuration

# 应用配置
app:
  name: "网络数据包分析工具"
  version: "1.0.0"
  description: "综合性网络数据包分析平台"
  debug: false
  timezone: "Asia/Shanghai"

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8000
  workers: 4
  reload: false
  access_log: true
  
# 数据库配置
database:
  url: "postgresql://netanalysis:password@localhost:5432/netanalysis"
  pool_size: 10
  max_overflow: 20
  echo: false
  pool_timeout: 30
  pool_recycle: 3600

# 缓存配置
cache:
  redis:
    url: "redis://localhost:6379/0"
    password: null
    db: 0
    max_connections: 10
    socket_timeout: 5
    socket_connect_timeout: 5

# 文件处理配置
files:
  upload:
    directory: "data/uploads"
    max_size: 1073741824  # 1GB
    allowed_extensions: ["pcap", "pcapng", "cap", "dmp", "txt"]
    temp_directory: "/tmp/netanalysis"
  
  results:
    directory: "data/results"
    retention_days: 30
    compression: true
  
  processing:
    chunk_size: 1048576  # 1MB
    max_memory: **********  # 2GB
    timeout: 3600  # 1小时

# 解析器配置
parsers:
  pcap:
    enabled: true
    library: "scapy"  # scapy, pyshark, dpkt
    options:
      lazy_loading: true
      filter_invalid: true
  
  pcapng:
    enabled: true
    library: "python-pcapng"
    options:
      extract_metadata: true
  
  tcpdump:
    enabled: true
    formats: ["text", "json"]
    options:
      parse_timestamps: true

# 分析器配置
analyzers:
  protocol:
    enabled: true
    layers: ["ethernet", "ip", "tcp", "udp", "http", "dns"]
    deep_inspection: true
  
  traffic:
    enabled: true
    metrics: ["bandwidth", "packets", "connections", "latency"]
    time_windows: [60, 300, 3600]  # 1分钟, 5分钟, 1小时
  
  anomaly:
    enabled: true
    algorithms: ["statistical", "rule_based", "ml_based"]
    sensitivity: "medium"  # low, medium, high
    thresholds:
      packet_rate: 1000  # packets/second
      bandwidth: 100000000  # 100Mbps
      connection_rate: 100  # connections/second
  
  security:
    enabled: true
    detection_rules:
      - "ddos_detection"
      - "port_scan_detection"
      - "malware_communication"
      - "data_exfiltration"

# AI集成配置
ai:
  enabled: true
  default_provider: "openai"
  
  providers:
    openai:
      model: "gpt-4"
      max_tokens: 4000
      temperature: 0.1
      timeout: 30
    
    anthropic:
      model: "claude-3-sonnet-20240229"
      max_tokens: 4000
      timeout: 30
    
    local:
      enabled: false
      model_path: "models/local_model"
      device: "cpu"
  
  prompts:
    directory: "src/netanalysis/ai/prompts"
    templates:
      anomaly_analysis: "anomaly_analysis.txt"
      security_assessment: "security_assessment.txt"
      performance_diagnosis: "performance_diagnosis.txt"
  
  caching:
    enabled: true
    ttl: 3600  # 1小时

# 可视化配置
visualization:
  charts:
    default_theme: "plotly_white"
    width: 800
    height: 600
    colors: ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd"]
  
  export:
    formats: ["png", "pdf", "svg", "html"]
    dpi: 300
    quality: 95
  
  interactive:
    enabled: true
    zoom: true
    pan: true
    select: true

# 任务队列配置
celery:
  broker_url: "redis://localhost:6379/1"
  result_backend: "redis://localhost:6379/2"
  task_serializer: "json"
  result_serializer: "json"
  accept_content: ["json"]
  timezone: "Asia/Shanghai"
  
  task_routes:
    "netanalysis.tasks.parse_file": {"queue": "parsing"}
    "netanalysis.tasks.analyze_traffic": {"queue": "analysis"}
    "netanalysis.tasks.generate_report": {"queue": "reporting"}
  
  worker_prefetch_multiplier: 1
  task_acks_late: true
  worker_max_tasks_per_child: 1000

# 安全配置
security:
  secret_key: "your-secret-key-here"
  jwt:
    algorithm: "HS256"
    expire_minutes: 1440  # 24小时
    refresh_expire_days: 7
  
  cors:
    allow_origins: ["http://localhost:3000", "http://localhost:8080"]
    allow_methods: ["GET", "POST", "PUT", "DELETE"]
    allow_headers: ["*"]
    allow_credentials: true
  
  rate_limiting:
    enabled: true
    requests_per_minute: 60
    burst_size: 10

# 监控配置
monitoring:
  prometheus:
    enabled: true
    port: 9090
    path: "/metrics"
  
  health_check:
    enabled: true
    path: "/health"
    checks: ["database", "redis", "disk_space"]
  
  logging:
    level: "INFO"
    format: "json"
    file: "logs/app.log"
    rotation: "1 day"
    retention: "30 days"
    
    loggers:
      netanalysis: "INFO"
      uvicorn: "INFO"
      sqlalchemy: "WARNING"

# 性能配置
performance:
  max_concurrent_analyses: 5
  analysis_timeout: 3600
  memory_limit: **********
  cpu_limit: 80  # 百分比
  
  caching:
    analysis_results: 3600  # 1小时
    file_metadata: 7200  # 2小时
    user_sessions: 86400  # 24小时

# 开发配置
development:
  debug: false
  reload: false
  api_docs: true
  swagger_ui: true
  redoc: true
  profiling: false
