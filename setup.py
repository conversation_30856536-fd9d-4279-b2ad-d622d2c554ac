#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络数据包分析工具安装配置
Network Packet Analysis Tool Setup Configuration
"""

from setuptools import setup, find_packages
import os

# 读取README文件作为长描述
def read_readme():
    """读取README文件内容"""
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "网络数据包分析工具"

# 读取requirements.txt文件
def read_requirements():
    """读取依赖包列表"""
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    requirements = []
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    requirements.append(line)
    return requirements

setup(
    name="network-packet-analysis",
    version="1.0.0",
    author="开发团队",
    author_email="<EMAIL>",
    description="综合性网络数据包分析工具",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/example/network-packet-analysis",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: System Administrators",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: System :: Networking :: Monitoring",
        "Topic :: Security",
        "Topic :: Internet :: Log Analysis",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.4.3",
            "pytest-asyncio>=0.21.1",
            "pytest-cov>=4.1.0",
            "black>=23.11.0",
            "isort>=5.12.0",
            "flake8>=6.1.0",
            "mypy>=1.7.1",
        ],
        "docs": [
            "mkdocs>=1.5.3",
            "mkdocs-material>=9.4.8",
        ],
    },
    entry_points={
        "console_scripts": [
            "netanalysis=netanalysis.cli.main:main",
            "netanalysis-server=netanalysis.server.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "netanalysis": [
            "templates/*.html",
            "static/css/*.css",
            "static/js/*.js",
            "config/*.yaml",
            "ai/prompts/*.txt",
        ],
    },
    zip_safe=False,
    keywords="network analysis packet capture security monitoring",
    project_urls={
        "Bug Reports": "https://github.com/example/network-packet-analysis/issues",
        "Source": "https://github.com/example/network-packet-analysis",
        "Documentation": "https://network-packet-analysis.readthedocs.io/",
    },
)
