# 网络数据包分析工具 (Network Packet Analysis Tool)

[![Python Version](https://img.shields.io/badge/python-3.8%2B-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Docker](https://img.shields.io/badge/docker-supported-blue.svg)](docker-compose.yml)
[![API Docs](https://img.shields.io/badge/api-docs-orange.svg)](http://localhost:8000/docs)

一个功能强大的综合性网络数据包分析平台，支持多种抓包格式的解析、深度分析和智能化洞察。

## ✨ 主要特性

### 🔍 多格式支持
- **Wireshark格式**：完整支持 `.pcap` 和 `.pcapng` 文件
- **tcpdump输出**：支持标准tcpdump文本和二进制格式
- **tshark输出**：支持JSON和文本格式解析
- **扩展性**：插件化架构，易于添加新格式支持

### 📊 深度分析
- **协议层级分析**：OSI七层协议完整解析
- **流量统计**：带宽、连接数、延迟等关键指标
- **异常检测**：基于规则和机器学习的异常识别
- **安全分析**：DDoS、端口扫描、恶意软件通信检测

### 🤖 AI增强分析
- **智能诊断**：集成大语言模型提供专家级分析建议
- **多模型支持**：OpenAI GPT-4、Claude、本地模型
- **上下文感知**：基于网络环境的个性化分析
- **自然语言查询**：支持中文自然语言查询分析

### 📈 可视化展示
- **交互式图表**：时序图、饼图、热力图、网络拓扑图
- **实时更新**：动态数据展示和实时监控
- **多格式导出**：PNG、PDF、SVG、HTML格式导出
- **自定义视图**：用户可保存和分享自定义分析视图

### 🌐 双界面支持
- **Web界面**：现代化的响应式Web应用
- **命令行工具**：强大的CLI工具，支持批处理和自动化
- **RESTful API**：完整的API接口，支持第三方集成

## 🚀 快速开始

### 使用Docker（推荐）

1. **克隆项目**
```bash
git clone https://github.com/example/network-packet-analysis.git
cd network-packet-analysis
```

2. **配置环境**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和AI服务密钥
```

3. **启动服务**
```bash
docker-compose up -d
```

4. **访问应用**
- Web界面: http://localhost:8000
- API文档: http://localhost:8000/docs
- Grafana监控: http://localhost:3000 (admin/admin123)

### 本地开发安装

1. **环境要求**
- Python 3.8+
- PostgreSQL 12+
- Redis 6+

2. **安装依赖**
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
pip install -e .
```

3. **配置数据库**
```bash
# 创建数据库
createdb netanalysis

# 运行迁移
alembic upgrade head
```

4. **启动服务**
```bash
# 启动Web服务
uvicorn netanalysis.api.main:app --reload

# 启动Worker（新终端）
celery -A netanalysis.tasks.celery worker --loglevel=info
```

## 📖 使用指南

### Web界面使用

1. **上传文件**：在Web界面中上传PCAP文件
2. **选择分析类型**：选择需要的分析模块
3. **查看结果**：浏览分析报告和可视化图表
4. **AI分析**：点击"AI分析"获取智能建议
5. **导出报告**：下载分析结果和图表

### 命令行使用

```bash
# 分析单个文件
netanalysis analyze sample.pcap --output report.json

# 批量分析
netanalysis batch-analyze *.pcap --format json

# 实时监控
netanalysis monitor --interface eth0 --duration 3600

# 生成报告
netanalysis report --input analysis.json --format pdf
```

### API使用

```python
import requests

# 上传文件
files = {'file': open('sample.pcap', 'rb')}
response = requests.post('http://localhost:8000/api/v1/files/upload', files=files)
file_id = response.json()['file_id']

# 开始分析
analysis_data = {'file_id': file_id, 'analysis_types': ['protocol', 'traffic', 'security']}
response = requests.post('http://localhost:8000/api/v1/analysis/start', json=analysis_data)
task_id = response.json()['task_id']

# 获取结果
response = requests.get(f'http://localhost:8000/api/v1/analysis/{task_id}/results')
results = response.json()
```

## 🏗️ 项目结构

```
network-packet-analysis/
├── src/netanalysis/           # 主要源代码
│   ├── core/                  # 核心分析引擎
│   ├── parsers/              # 数据包解析器
│   ├── analyzers/            # 分析算法
│   ├── ai/                   # AI集成模块
│   ├── visualization/        # 可视化引擎
│   ├── api/                  # Web API
│   └── cli/                  # 命令行工具
├── frontend/                 # Web前端
├── tests/                    # 测试代码
├── docs/                     # 文档
├── config/                   # 配置文件
├── docker/                   # Docker配置
└── scripts/                  # 工具脚本
```

## 🔧 配置说明

### 环境变量配置

主要环境变量说明：

```bash
# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost:5432/netanalysis

# Redis配置
REDIS_URL=redis://localhost:6379/0

# AI服务配置
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# 文件存储配置
UPLOAD_DIR=data/uploads
MAX_UPLOAD_SIZE=1073741824  # 1GB
```

### 高级配置

详细配置请参考 `config/settings.yaml` 文件，支持：
- 解析器参数调优
- 分析算法配置
- AI模型选择
- 性能优化设置
- 安全策略配置

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/unit/test_parser.py

# 生成覆盖率报告
pytest --cov=netanalysis --cov-report=html
```

## 📚 文档

- [需求分析文档](docs/requirements.md)
- [系统设计文档](docs/system_design.md)
- [开发路线图](docs/development_roadmap.md)
- [API文档](http://localhost:8000/docs)
- [用户手册](docs/user/manual.md)
- [开发者指南](docs/dev/guide.md)

## 🤝 贡献指南

我们欢迎所有形式的贡献！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

### 开发流程

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Scapy](https://scapy.net/) - 强大的数据包操作库
- [FastAPI](https://fastapi.tiangolo.com/) - 现代化的Web框架
- [Plotly](https://plotly.com/) - 交互式可视化库
- [Wireshark](https://www.wireshark.org/) - 网络协议分析器

## 📞 联系我们

- 项目主页: https://github.com/example/network-packet-analysis
- 问题反馈: https://github.com/example/network-packet-analysis/issues
- 邮箱: <EMAIL>

---

**注意**: 本工具仅用于合法的网络分析和安全研究目的。请确保在使用时遵守相关法律法规和网络使用政策。
