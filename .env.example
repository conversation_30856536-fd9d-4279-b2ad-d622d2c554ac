# 网络数据包分析工具环境配置示例
# Network Packet Analysis Tool Environment Configuration Example

# 应用基础配置
APP_NAME="网络数据包分析工具"
APP_VERSION="1.0.0"
APP_DESCRIPTION="综合性网络数据包分析平台"
DEBUG=false
LOG_LEVEL=INFO

# 服务器配置
HOST=0.0.0.0
PORT=8000
WORKERS=4
RELOAD=false

# 数据库配置
DATABASE_URL=postgresql://netanalysis:password@localhost:5432/netanalysis
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_ECHO=false

# Redis缓存配置
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_CONNECTIONS=10

# 文件存储配置
UPLOAD_DIR=data/uploads
RESULTS_DIR=data/results
MAX_UPLOAD_SIZE=**********  # 1GB
ALLOWED_EXTENSIONS=pcap,pcapng,cap,dmp

# AI服务配置
# OpenAI配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.1

# Claude配置
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-sonnet-20240229
ANTHROPIC_MAX_TOKENS=4000

# 默认AI提供商 (openai, anthropic, local)
DEFAULT_AI_PROVIDER=openai

# 安全配置
SECRET_KEY=your-secret-key-here-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440  # 24小时
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# 任务队列配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=["json"]
CELERY_TIMEZONE=Asia/Shanghai

# 监控配置
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
METRICS_ENABLED=true

# 日志配置
LOG_FORMAT=json
LOG_FILE=logs/app.log
LOG_ROTATION=1 day
LOG_RETENTION=30 days

# 性能配置
MAX_CONCURRENT_ANALYSES=5
ANALYSIS_TIMEOUT=3600  # 1小时
MEMORY_LIMIT=**********  # 2GB
CHUNK_SIZE=1048576  # 1MB

# 可视化配置
CHART_WIDTH=800
CHART_HEIGHT=600
CHART_THEME=plotly_white
EXPORT_FORMATS=["png", "pdf", "svg", "html"]

# 开发配置
DEVELOPMENT_MODE=false
API_DOCS_ENABLED=true
SWAGGER_UI_ENABLED=true
REDOC_ENABLED=true

# 测试配置
TEST_DATABASE_URL=postgresql://test:test@localhost:5432/test_netanalysis
TEST_REDIS_URL=redis://localhost:6379/15
