# 网络数据包分析工具 Docker 镜像
# Network Packet Analysis Tool Docker Image

# 使用官方Python 3.11镜像作为基础镜像
FROM python:3.11-slim

# 设置维护者信息
LABEL maintainer="<EMAIL>"
LABEL description="网络数据包分析工具"
LABEL version="1.0.0"

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    # 网络工具
    tcpdump \
    tshark \
    wireshark-common \
    # 编译工具
    gcc \
    g++ \
    make \
    # 系统库
    libpcap-dev \
    libssl-dev \
    libffi-dev \
    # 其他工具
    curl \
    wget \
    git \
    # 清理缓存
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN groupadd -r netanalysis && useradd -r -g netanalysis netanalysis

# 创建必要的目录
RUN mkdir -p /app/data/uploads \
    /app/data/results \
    /app/data/temp \
    /app/logs \
    /app/config \
    && chown -R netanalysis:netanalysis /app

# 复制requirements文件
COPY requirements.txt /app/

# 安装Python依赖
RUN pip install --upgrade pip setuptools wheel && \
    pip install -r requirements.txt

# 复制应用代码
COPY src/ /app/src/
COPY config/ /app/config/
COPY scripts/ /app/scripts/
COPY setup.py pyproject.toml /app/

# 安装应用
RUN pip install -e .

# 复制启动脚本
COPY docker/entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

# 创建数据目录的占位文件
RUN touch /app/data/uploads/.gitkeep \
    /app/data/results/.gitkeep \
    /app/data/temp/.gitkeep

# 设置文件权限
RUN chown -R netanalysis:netanalysis /app

# 切换到非root用户
USER netanalysis

# 暴露端口
EXPOSE 8000 9090

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 设置入口点
ENTRYPOINT ["/app/entrypoint.sh"]

# 默认命令
CMD ["server"]
