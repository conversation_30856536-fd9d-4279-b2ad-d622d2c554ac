#!/bin/bash
# 网络数据包分析工具 Docker 入口脚本
# Network Packet Analysis Tool Docker Entrypoint Script

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [ "${DEBUG:-false}" = "true" ]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# 等待服务就绪
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local timeout=${4:-30}
    
    log_info "等待 $service_name 服务就绪 ($host:$port)..."
    
    for i in $(seq 1 $timeout); do
        if nc -z "$host" "$port" 2>/dev/null; then
            log_info "$service_name 服务已就绪"
            return 0
        fi
        log_debug "等待 $service_name 服务... ($i/$timeout)"
        sleep 1
    done
    
    log_error "$service_name 服务在 $timeout 秒内未就绪"
    return 1
}

# 检查环境变量
check_env_vars() {
    log_info "检查环境变量..."
    
    local required_vars=(
        "DATABASE_URL"
        "REDIS_URL"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "必需的环境变量 $var 未设置"
            exit 1
        fi
        log_debug "$var = ${!var}"
    done
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 等待数据库就绪
    if [[ $DATABASE_URL == postgresql* ]]; then
        local db_host=$(echo $DATABASE_URL | sed -n 's/.*@\([^:]*\):.*/\1/p')
        local db_port=$(echo $DATABASE_URL | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
        wait_for_service "$db_host" "$db_port" "PostgreSQL"
    fi
    
    # 运行数据库迁移
    log_info "运行数据库迁移..."
    python -m alembic upgrade head || {
        log_warn "数据库迁移失败，尝试初始化数据库..."
        python -c "
from netanalysis.models import Base
from netanalysis.config import get_database_engine
engine = get_database_engine()
Base.metadata.create_all(engine)
print('数据库表创建完成')
" || log_error "数据库初始化失败"
    }
}

# 初始化Redis
init_redis() {
    log_info "检查Redis连接..."
    
    local redis_host=$(echo $REDIS_URL | sed -n 's/redis:\/\/\([^:]*\):.*/\1/p')
    local redis_port=$(echo $REDIS_URL | sed -n 's/redis:\/\/[^:]*:\([0-9]*\).*/\1/p')
    
    wait_for_service "$redis_host" "$redis_port" "Redis"
    
    # 测试Redis连接
    python -c "
import redis
import os
r = redis.from_url(os.getenv('REDIS_URL'))
r.ping()
print('Redis连接正常')
" || {
        log_error "Redis连接失败"
        exit 1
    }
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    local dirs=(
        "/app/data/uploads"
        "/app/data/results"
        "/app/data/temp"
        "/app/logs"
    )
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_debug "创建目录: $dir"
        fi
    done
    
    # 设置权限
    chmod 755 /app/data/uploads /app/data/results /app/data/temp
    chmod 755 /app/logs
}

# 启动Web服务器
start_server() {
    log_info "启动Web服务器..."
    
    # 设置默认配置
    export HOST=${HOST:-0.0.0.0}
    export PORT=${PORT:-8000}
    export WORKERS=${WORKERS:-4}
    
    log_info "服务器配置: $HOST:$PORT (workers: $WORKERS)"
    
    # 启动服务器
    exec gunicorn netanalysis.api.main:app \
        --bind "$HOST:$PORT" \
        --workers "$WORKERS" \
        --worker-class uvicorn.workers.UvicornWorker \
        --access-logfile - \
        --error-logfile - \
        --log-level info \
        --timeout 300 \
        --keep-alive 2 \
        --max-requests 1000 \
        --max-requests-jitter 100
}

# 启动Celery工作进程
start_worker() {
    log_info "启动Celery工作进程..."
    
    # 设置默认配置
    export CELERY_CONCURRENCY=${CELERY_CONCURRENCY:-4}
    export CELERY_LOGLEVEL=${CELERY_LOGLEVEL:-info}
    
    log_info "Worker配置: 并发数=$CELERY_CONCURRENCY, 日志级别=$CELERY_LOGLEVEL"
    
    # 启动worker
    exec celery -A netanalysis.tasks.celery worker \
        --loglevel="$CELERY_LOGLEVEL" \
        --concurrency="$CELERY_CONCURRENCY" \
        --max-tasks-per-child=1000 \
        --time-limit=3600 \
        --soft-time-limit=3300
}

# 启动Flower监控
start_flower() {
    log_info "启动Flower监控..."
    
    exec celery -A netanalysis.tasks.celery flower \
        --port=5555 \
        --broker="$CELERY_BROKER_URL"
}

# 运行命令行工具
run_cli() {
    log_info "运行命令行工具..."
    shift # 移除 'cli' 参数
    exec python -m netanalysis.cli.main "$@"
}

# 运行数据库迁移
run_migrate() {
    log_info "运行数据库迁移..."
    init_database
    log_info "数据库迁移完成"
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    shift # 移除 'test' 参数
    exec python -m pytest "$@"
}

# 主函数
main() {
    log_info "网络数据包分析工具启动中..."
    log_info "命令: $*"
    
    # 检查环境变量
    check_env_vars
    
    # 创建必要的目录
    create_directories
    
    # 根据命令执行相应操作
    case "$1" in
        "server")
            init_database
            init_redis
            start_server
            ;;
        "worker")
            init_database
            init_redis
            start_worker
            ;;
        "flower")
            init_redis
            start_flower
            ;;
        "cli")
            run_cli "$@"
            ;;
        "migrate")
            run_migrate
            ;;
        "test")
            run_tests "$@"
            ;;
        "bash"|"sh")
            log_info "启动交互式shell..."
            exec /bin/bash
            ;;
        *)
            log_info "可用命令:"
            log_info "  server  - 启动Web服务器"
            log_info "  worker  - 启动Celery工作进程"
            log_info "  flower  - 启动Flower监控"
            log_info "  cli     - 运行命令行工具"
            log_info "  migrate - 运行数据库迁移"
            log_info "  test    - 运行测试"
            log_info "  bash    - 启动交互式shell"
            
            if [ $# -gt 0 ]; then
                log_info "执行自定义命令: $*"
                exec "$@"
            else
                log_error "请指定要执行的命令"
                exit 1
            fi
            ;;
    esac
}

# 信号处理
trap 'log_info "收到终止信号，正在关闭..."; exit 0' SIGTERM SIGINT

# 执行主函数
main "$@"
