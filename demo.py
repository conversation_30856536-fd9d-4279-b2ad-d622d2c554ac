#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络数据包分析工具演示脚本
Network Packet Analysis Tool Demo Script

演示如何使用网络数据包分析工具的核心功能：
1. 解析PCAP文件
2. 提取文件元数据
3. 分析数据包信息
4. 生成统计报告

使用方法:
    python demo.py [pcap_file_path]
"""

import sys
import os
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from netanalysis.parsers.pcap_parser import PcapParser
from netanalysis.core.parser import parser_registry
from netanalysis.core.utils import format_bytes, format_duration
from netanalysis.core.exceptions import NetAnalysisError


def create_sample_pcap():
    """
    创建一个简单的示例PCAP文件用于演示
    
    Returns:
        str: 创建的PCAP文件路径
    """
    import struct
    import tempfile
    from datetime import datetime
    
    # 创建临时PCAP文件
    with tempfile.NamedTemporaryFile(suffix='.pcap', delete=False) as f:
        # PCAP全局头部
        global_header = struct.pack('<IHHIIII',
            0xa1b2c3d4,  # 魔数
            2, 4,        # 版本号
            0,           # 时区偏移
            0,           # 时间戳精度
            65535,       # 快照长度
            1            # 数据链路类型（以太网）
        )
        f.write(global_header)
        
        # 添加一个简单的数据包
        timestamp = int(datetime.now().timestamp())
        packet_data = b'\x00' * 64  # 64字节的空数据包
        
        # 数据包头部
        packet_header = struct.pack('<IIII',
            timestamp,      # 时间戳（秒）
            0,              # 时间戳（微秒）
            len(packet_data),  # 捕获长度
            len(packet_data)   # 原始长度
        )
        f.write(packet_header)
        f.write(packet_data)
        
        return f.name


def demo_parser_registry():
    """演示解析器注册表功能"""
    print("=" * 60)
    print("解析器注册表演示")
    print("=" * 60)
    
    # 导入解析器模块以触发自动注册
    import netanalysis.parsers
    
    print(f"已注册的解析器: {parser_registry.list_parsers()}")
    print(f"支持的文件格式: {parser_registry.get_supported_formats()}")
    print()


def demo_pcap_parsing(pcap_file: str):
    """
    演示PCAP文件解析功能
    
    Args:
        pcap_file: PCAP文件路径
    """
    print("=" * 60)
    print(f"PCAP文件解析演示: {pcap_file}")
    print("=" * 60)
    
    try:
        # 创建PCAP解析器
        parser = PcapParser()
        
        # 1. 验证文件
        print("1. 文件验证...")
        if parser.validate_file(pcap_file):
            print("   ✓ 文件验证通过")
        else:
            print("   ✗ 文件验证失败")
            return
        
        # 2. 检查是否可以解析
        print("2. 格式检查...")
        if parser.can_parse(pcap_file):
            print("   ✓ 支持解析该文件格式")
        else:
            print("   ✗ 不支持该文件格式")
            return
        
        # 3. 获取文件元数据
        print("3. 获取文件元数据...")
        metadata = parser.get_file_metadata(pcap_file)
        print(f"   文件名: {metadata.filename}")
        print(f"   文件大小: {format_bytes(metadata.file_size)}")
        print(f"   文件格式: {metadata.file_format}")
        print(f"   文件哈希: {metadata.file_hash[:16]}...")
        print(f"   数据包数量: {metadata.total_packets}")
        if metadata.comments:
            print(f"   注释信息: {metadata.comments}")
        
        # 4. 解析数据包
        print("4. 解析数据包...")
        packets = list(parser.parse_file(pcap_file))
        print(f"   成功解析 {len(packets)} 个数据包")
        
        # 5. 显示解析统计
        print("5. 解析统计信息...")
        stats = parser.get_parse_stats()
        print(f"   总数据包: {stats['total_packets']}")
        print(f"   解析成功: {stats['parsed_packets']}")
        print(f"   解析失败: {stats['failed_packets']}")
        if 'duration_seconds' in stats:
            print(f"   解析耗时: {format_duration(stats['duration_seconds'])}")
        
        # 6. 显示前几个数据包的详细信息
        if packets:
            print("6. 数据包详细信息（前3个）...")
            for i, packet in enumerate(packets[:3]):
                print(f"   数据包 #{i+1}:")
                print(f"     时间戳: {packet.timestamp}")
                print(f"     大小: {packet.size} 字节")
                print(f"     协议: {packet.protocol.value}")
                if packet.src_ip and packet.dst_ip:
                    print(f"     源地址: {packet.src_ip}")
                    print(f"     目标地址: {packet.dst_ip}")
                if packet.src_port and packet.dst_port:
                    print(f"     源端口: {packet.src_port}")
                    print(f"     目标端口: {packet.dst_port}")
                print(f"     方向: {packet.direction.value}")
                print()
        
    except NetAnalysisError as e:
        print(f"   ✗ 分析错误: {e}")
    except Exception as e:
        print(f"   ✗ 未知错误: {e}")


def demo_auto_parser_selection(pcap_file: str):
    """
    演示自动解析器选择功能
    
    Args:
        pcap_file: 文件路径
    """
    print("=" * 60)
    print("自动解析器选择演示")
    print("=" * 60)
    
    try:
        # 导入解析器模块以触发自动注册
        import netanalysis.parsers
        
        # 自动选择合适的解析器
        parser = parser_registry.get_parser_for_file(pcap_file)
        
        if parser:
            print(f"为文件 {pcap_file} 自动选择解析器: {parser.__class__.__name__}")
            print(f"支持的扩展名: {parser.supported_extensions}")
            
            # 使用选择的解析器解析文件
            packets = list(parser.parse_file(pcap_file))
            print(f"解析结果: {len(packets)} 个数据包")
        else:
            print(f"未找到适合文件 {pcap_file} 的解析器")
    
    except Exception as e:
        print(f"自动选择解析器失败: {e}")


def main():
    """主函数"""
    print("网络数据包分析工具演示")
    print("Network Packet Analysis Tool Demo")
    print()
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        pcap_file = sys.argv[1]
        if not os.path.exists(pcap_file):
            print(f"错误: 文件不存在 - {pcap_file}")
            return
    else:
        # 创建示例PCAP文件
        print("未指定PCAP文件，创建示例文件...")
        pcap_file = create_sample_pcap()
        print(f"创建示例文件: {pcap_file}")
        print()
    
    try:
        # 演示各种功能
        demo_parser_registry()
        demo_pcap_parsing(pcap_file)
        demo_auto_parser_selection(pcap_file)
        
        print("=" * 60)
        print("演示完成！")
        print("=" * 60)
        
    finally:
        # 清理临时文件
        if len(sys.argv) <= 1 and os.path.exists(pcap_file):
            os.unlink(pcap_file)
            print(f"清理临时文件: {pcap_file}")


if __name__ == "__main__":
    main()
