[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "network-packet-analysis"
version = "1.0.0"
description = "综合性网络数据包分析工具"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "开发团队", email = "<EMAIL>"}
]
maintainers = [
    {name = "开发团队", email = "<EMAIL>"}
]
keywords = ["network", "analysis", "packet", "capture", "security", "monitoring"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: System Administrators",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: System :: Networking :: Monitoring",
    "Topic :: Security",
    "Topic :: Internet :: Log Analysis",
]
requires-python = ">=3.8"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "scapy>=2.5.0",
    "pandas>=2.1.4",
    "numpy>=1.24.4",
    "plotly>=5.17.0",
    "sqlalchemy>=2.0.23",
    "redis>=5.0.1",
    "click>=8.1.7",
    "httpx>=0.25.2",
    "pydantic>=2.5.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
    "pre-commit>=3.6.0",
]
docs = [
    "mkdocs>=1.5.3",
    "mkdocs-material>=9.4.8",
    "mkdocs-mermaid2-plugin>=1.1.1",
]
ai = [
    "openai>=1.3.8",
    "anthropic>=0.7.8",
]
viz = [
    "matplotlib>=3.8.2",
    "seaborn>=0.13.0",
    "bokeh>=3.3.2",
]

[project.scripts]
netanalysis = "netanalysis.cli.main:main"
netanalysis-server = "netanalysis.server.main:main"

[project.urls]
Homepage = "https://github.com/example/network-packet-analysis"
Documentation = "https://network-packet-analysis.readthedocs.io/"
Repository = "https://github.com/example/network-packet-analysis"
"Bug Tracker" = "https://github.com/example/network-packet-analysis/issues"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
netanalysis = [
    "templates/*.html",
    "static/css/*.css",
    "static/js/*.js",
    "config/*.yaml",
    "ai/prompts/*.txt",
]

# Black代码格式化配置
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # 排除的目录
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

# isort导入排序配置
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["netanalysis"]
known_third_party = ["fastapi", "scapy", "pandas", "numpy", "plotly"]

# MyPy类型检查配置
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "scapy.*",
    "pyshark.*",
    "dpkt.*",
    "plotly.*",
    "matplotlib.*",
    "seaborn.*",
    "bokeh.*",
]
ignore_missing_imports = true

# Pytest测试配置
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

# Coverage配置
[tool.coverage.run]
source = ["src/netanalysis"]
omit = [
    "*/tests/*",
    "*/test_*.py",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
