# 网络数据包分析工具 Git忽略文件
# Network Packet Analysis Tool Git Ignore File

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试和覆盖率
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath
*.sage.py

# 环境变量
.env
.env.local
.env.development
.env.test
.env.production
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre
.pyre/

# 数据文件
data/uploads/*
data/results/*
data/temp/*
!data/uploads/.gitkeep
!data/results/.gitkeep
!data/temp/.gitkeep

# 日志文件
logs/*.log
logs/*.log.*
*.log

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 配置文件（包含敏感信息）
config/local.yaml
config/production.yaml
.env.local
.env.production

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# 前端相关
frontend/node_modules/
frontend/dist/
frontend/.nuxt/
frontend/.output/
frontend/.vite/
frontend/.cache/
frontend/coverage/

# 临时文件
*.tmp
*.temp
*.bak
*.backup
*.orig

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 大文件和二进制文件
*.pcap
*.pcapng
*.cap
*.dmp
!data/samples/*.pcap
!data/samples/*.pcapng

# 模型文件
models/*.bin
models/*.pkl
models/*.joblib
models/*.h5
models/*.pb

# 证书和密钥
*.pem
*.key
*.crt
*.p12
*.pfx

# Docker
.dockerignore
docker-compose.override.yml

# Kubernetes
k8s/secrets/
k8s/configmaps/

# 监控和指标
prometheus/
grafana/data/

# 备份文件
backup/
*.backup

# 性能分析文件
*.prof
*.pstats

# 本地开发工具
.local/
.cache/

# 文档构建
docs/_build/
docs/build/

# 测试相关
.coverage.*
htmlcov/
.pytest_cache/
test-results/

# 安全扫描结果
security-scan-results/
vulnerability-reports/
