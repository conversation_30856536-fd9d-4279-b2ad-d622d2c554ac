# 网络数据包分析工具 Docker Compose 配置
# Network Packet Analysis Tool Docker Compose Configuration

version: '3.8'

services:
  # 主应用服务
  web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: netanalysis-web
    restart: unless-stopped
    ports:
      - "8000:8000"  # Web API端口
      - "9090:9090"  # Prometheus指标端口
    environment:
      - DATABASE_URL=***********************************************/netanalysis
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
      - LOG_LEVEL=INFO
      - DEBUG=false
    volumes:
      - ./data/uploads:/app/data/uploads
      - ./data/results:/app/data/results
      - ./logs:/app/logs
      - ./config/local.yaml:/app/config/local.yaml:ro
    depends_on:
      - db
      - redis
    networks:
      - netanalysis-network
    command: ["server"]

  # Celery工作进程
  worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: netanalysis-worker
    restart: unless-stopped
    environment:
      - DATABASE_URL=***********************************************/netanalysis
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
      - LOG_LEVEL=INFO
    volumes:
      - ./data/uploads:/app/data/uploads
      - ./data/results:/app/data/results
      - ./logs:/app/logs
      - ./config/local.yaml:/app/config/local.yaml:ro
    depends_on:
      - db
      - redis
    networks:
      - netanalysis-network
    command: ["worker"]

  # Celery监控
  flower:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: netanalysis-flower
    restart: unless-stopped
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - redis
    networks:
      - netanalysis-network
    command: ["flower"]

  # PostgreSQL数据库
  db:
    image: postgres:15-alpine
    container_name: netanalysis-db
    restart: unless-stopped
    environment:
      - POSTGRES_DB=netanalysis
      - POSTGRES_USER=netanalysis
      - POSTGRES_PASSWORD=netanalysis123
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - netanalysis-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U netanalysis -d netanalysis"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: netanalysis-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - netanalysis-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: netanalysis-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/ssl:/etc/nginx/ssl:ro
      - ./frontend/dist:/usr/share/nginx/html:ro
    depends_on:
      - web
    networks:
      - netanalysis-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: netanalysis-prometheus
    restart: unless-stopped
    ports:
      - "9091:9090"
    volumes:
      - ./docker/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - netanalysis-network

  # Grafana仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: netanalysis-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - netanalysis-network

# 网络配置
networks:
  netanalysis-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
