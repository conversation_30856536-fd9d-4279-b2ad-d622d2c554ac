["tests/unit/test_pcap_parser.py::TestPcapParser::test_can_parse_invalid_file", "tests/unit/test_pcap_parser.py::TestPcapParser::test_can_parse_valid_pcap_file", "tests/unit/test_pcap_parser.py::TestPcapParser::test_can_parse_wrong_extension", "tests/unit/test_pcap_parser.py::TestPcapParser::test_determine_packet_direction", "tests/unit/test_pcap_parser.py::TestPcapParser::test_get_file_metadata", "tests/unit/test_pcap_parser.py::TestPcapParser::test_packet_validation", "tests/unit/test_pcap_parser.py::TestPcapParser::test_parse_empty_pcap_file", "tests/unit/test_pcap_parser.py::TestPcapParser::test_parse_invalid_file_format", "tests/unit/test_pcap_parser.py::TestPcapParser::test_parser_config", "tests/unit/test_pcap_parser.py::TestPcapParser::test_stats_tracking", "tests/unit/test_pcap_parser.py::TestPcapParser::test_string_representation", "tests/unit/test_pcap_parser.py::TestPcapParser::test_supported_extensions", "tests/unit/test_pcap_parser.py::TestPcapParserIntegration::test_full_parsing_workflow"]